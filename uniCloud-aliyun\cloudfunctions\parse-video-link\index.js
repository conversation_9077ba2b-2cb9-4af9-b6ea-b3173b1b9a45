"use strict";

/**
 * 视频解析云函数
 * 基于 parse-video-py 项目实现
 * 支持抖音和小红书视频/图集解析
 */

const db = uniCloud.database();

/**
 * 获取默认请求头
 * @param {string} platform 平台类型 'douyin' | 'redbook'
 * @returns {Object} 请求头对象
 */
function getDefaultHeaders(platform = 'douyin') {
  if (platform === 'redbook') {
    // 小红书使用 Windows 设备的 User-Agent
    const windowsUserAgents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ];
    const randomUserAgent = windowsUserAgents[Math.floor(Math.random() * windowsUserAgents.length)];
    return {
      "User-Agent": randomUserAgent
    };
  } else {
    // 抖音使用 iOS 设备的 User-Agent
    const iosUserAgents = [
      "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1"
    ];
    const randomUserAgent = iosUserAgents[Math.floor(Math.random() * iosUserAgents.length)];
    return {
      "User-Agent": randomUserAgent
    };
  }
}

/**
 * 根据视频ID构建请求URL
 * @param {string} videoId 视频ID
 * @returns {string} 请求URL
 */
function getRequestUrlByVideoId(videoId) {
  return `https://www.iesdouyin.com/share/video/${videoId}/`;
}

/**
 * 获取视频重定向URL
 * @param {string} videoUrl 原始视频URL
 * @returns {Promise<string>} 重定向后的URL
 */
async function getVideoRedirectUrl(videoUrl) {
  try {
    const response = await uniCloud.httpclient.request(videoUrl, {
      method: "GET",
      headers: getDefaultHeaders('douyin'),
      followRedirect: false,
      timeout: 10000
    });
    
    // 返回重定向后的地址，如果没有重定向则返回原地址(抖音中的西瓜视频,重定向地址为空)
    return response.headers.location || videoUrl;
  } catch (error) {
    console.warn("获取重定向URL失败:", error.message);
    return videoUrl;
  }
}

/**
 * 解析分享URL获取视频信息
 * @param {string} shareUrl 分享URL
 * @returns {Promise<Object>} 视频信息
 */
async function parseShareUrl(shareUrl) {
  console.log("开始解析分享URL:", shareUrl);

  let requestUrl = shareUrl;

  if (shareUrl.startsWith("https://www.douyin.com/video/")) {
    // 支持电脑网页版链接 https://www.douyin.com/video/xxxxxx
    console.log("检测到网页版链接格式");
    const videoId = shareUrl.strip ? shareUrl.strip("/").split("/").pop() : shareUrl.replace(/\/$/, "").split("/").pop();
    console.log("提取的视频ID:", videoId);
    requestUrl = getRequestUrlByVideoId(videoId);
  } else {
    // 支持app分享链接 https://v.douyin.com/xxxxxx
    console.log("检测到短链接格式，开始获取重定向URL");

    try {
      const shareResponse = await uniCloud.httpclient.request(shareUrl, {
        method: "GET",
        headers: getDefaultHeaders('douyin'),
        followRedirect: false,
        timeout: 15000
      });

      console.log("短链接响应状态:", shareResponse.status);
      console.log("响应头:", JSON.stringify(shareResponse.headers, null, 2));

      const location = shareResponse.headers.location;
      if (!location) {
        console.error("重定向响应中没有location头");
        throw new Error("无法获取重定向URL，可能是链接已失效");
      }

      console.log("重定向URL:", location);
      const videoId = location.split("?")[0].replace(/\/$/, "").split("/").pop();
      console.log("从重定向URL提取的视频ID:", videoId);
      requestUrl = getRequestUrlByVideoId(videoId);
    } catch (redirectError) {
      console.error("获取重定向URL失败:", redirectError);
      throw new Error(`链接重定向失败: ${redirectError.message}`);
    }
  }

  console.log("最终请求URL:", requestUrl);
  
  // 获取视频页面内容
  console.log("开始请求视频页面内容...");
  const response = await uniCloud.httpclient.request(requestUrl, {
    method: "GET",
    headers: getDefaultHeaders('douyin'),
    followRedirect: true,
    timeout: 15000
  });

  console.log("页面请求响应状态:", response.status);
  if (response.status !== 200) {
    throw new Error(`HTTP请求失败，状态码: ${response.status}`);
  }

  const htmlContent = response.data;
  console.log("HTML内容长度:", htmlContent.length);
  console.log("HTML内容开头:", htmlContent.substring(0, 500));

  // 检查页面是否包含抖音相关内容
  if (!htmlContent.includes('douyin') && !htmlContent.includes('抖音')) {
    console.warn("页面内容可能不是抖音页面");
    console.log("页面标题检查:", htmlContent.match(/<title[^>]*>([^<]*)<\/title>/i));
  }

  // 使用正则表达式查找 window._ROUTER_DATA
  console.log("开始查找 window._ROUTER_DATA...");
  const pattern = /window\._ROUTER_DATA\s*=\s*(.*?)<\/script>/s;
  const match = pattern.exec(htmlContent);

  if (!match || !match[1]) {
    console.error("未找到 window._ROUTER_DATA");
    // 尝试查找其他可能的数据结构
    const alternativePatterns = [
      /window\.__INITIAL_STATE__\s*=\s*(.*?)<\/script>/s,
      /window\.__NUXT__\s*=\s*(.*?)<\/script>/s,
      /"videoData":\s*({.*?})/s
    ];

    for (let i = 0; i < alternativePatterns.length; i++) {
      const altMatch = alternativePatterns[i].exec(htmlContent);
      if (altMatch) {
        console.log(`找到替代数据结构 ${i + 1}:`, altMatch[0].substring(0, 100));
      }
    }

    throw new Error("页面数据结构已变更，无法找到视频信息数据");
  }

  console.log("找到 _ROUTER_DATA，数据长度:", match[1].length);
  console.log("JSON数据开头:", match[1].substring(0, 200));

  let jsonData;
  try {
    jsonData = JSON.parse(match[1].trim());
    console.log("JSON解析成功");
    console.log("JSON数据结构键:", Object.keys(jsonData));
  } catch (error) {
    console.error("JSON解析失败:", error.message);
    console.log("解析失败的数据开头:", match[1].substring(0, 500));
    throw new Error(`JSON数据解析失败: ${error.message}`);
  }
  
  // 获取链接返回json数据进行视频和图集判断,如果指定类型不存在，抛出异常
  console.log("开始解析视频数据结构...");
  console.log("loaderData 键:", Object.keys(jsonData.loaderData || {}));

  // 返回的json数据中，视频字典类型为 video_(id)/page
  const VIDEO_ID_PAGE_KEY = "video_(id)/page";
  // 返回的json数据中，图集字典类型为 note_(id)/page
  const NOTE_ID_PAGE_KEY = "note_(id)/page";

  let originalVideoInfo;
  let contentType = "unknown";

  if (VIDEO_ID_PAGE_KEY in jsonData.loaderData) {
    console.log("检测到视频内容类型");
    originalVideoInfo = jsonData.loaderData[VIDEO_ID_PAGE_KEY].videoInfoRes;
    contentType = "video";
  } else if (NOTE_ID_PAGE_KEY in jsonData.loaderData) {
    console.log("检测到图集内容类型");
    originalVideoInfo = jsonData.loaderData[NOTE_ID_PAGE_KEY].videoInfoRes;
    contentType = "note";
  } else {
    console.error("未找到已知的内容类型");
    console.log("可用的 loaderData 键:", Object.keys(jsonData.loaderData || {}));

    // 尝试查找其他可能的键
    const possibleKeys = Object.keys(jsonData.loaderData || {}).filter(key =>
      key.includes('video') || key.includes('note') || key.includes('page')
    );
    console.log("可能的内容键:", possibleKeys);

    throw new Error("无法识别内容类型，可能是页面结构已变更");
  }

  console.log("内容类型:", contentType);
  console.log("原始视频信息结构:", originalVideoInfo ? Object.keys(originalVideoInfo) : "null");

  // 如果没有视频信息，获取并抛出异常
  if (!originalVideoInfo || !originalVideoInfo.item_list || originalVideoInfo.item_list.length === 0) {
    console.error("视频信息为空或无效");

    let errDetailMsg = "内容不存在或已被删除";
    if (originalVideoInfo && originalVideoInfo.filter_list && originalVideoInfo.filter_list.length > 0) {
      errDetailMsg = originalVideoInfo.filter_list[0].detail_msg || errDetailMsg;
      console.log("过滤器错误信息:", errDetailMsg);
    }

    // 如果是图集类型，提供更明确的错误信息
    if (contentType === "note") {
      throw new Error("该链接指向的是图集内容，本服务仅支持视频处理，请选择包含视频的抖音链接");
    }

    throw new Error(errDetailMsg);
  }
  
  const data = originalVideoInfo.item_list[0];
  console.log("视频数据项结构:", Object.keys(data));

  // 获取图集图片地址
  const images = [];
  // 如果data含有 images，并且 images 是一个列表
  if ("images" in data && Array.isArray(data.images)) {
    console.log("检测到图集内容，图片数量:", data.images.length);
    // 获取每个图片的url_list中的第一个元素，非空时添加到images列表中
    for (const img of data.images) {
      if (
        "url_list" in img &&
        Array.isArray(img.url_list) &&
        img.url_list.length > 0 &&
        img.url_list[0].length > 0
      ) {
        images.push({
          url: img.url_list[0]
        });
      }
    }
    console.log("成功解析图片数量:", images.length);
  }

  // 获取视频播放地址
  let videoUrl = "";
  console.log("检查视频数据结构:", {
    hasVideo: !!data.video,
    hasPlayAddr: !!(data.video && data.video.play_addr),
    hasUrlList: !!(data.video && data.video.play_addr && data.video.play_addr.url_list)
  });

  if (data.video && data.video.play_addr && data.video.play_addr.url_list && data.video.play_addr.url_list.length > 0) {
    videoUrl = data.video.play_addr.url_list[0].replace("playwm", "play");
    console.log("提取到视频URL:", videoUrl);
  } else {
    console.log("未找到有效的视频播放地址");
  }

  // 如果图集地址不为空时，因为没有视频，上面抖音返回的视频地址无法访问，置空处理
  if (images.length > 0) {
    console.log("内容为图集，清空视频URL");
    videoUrl = "";

    // 如果是图集，直接抛出错误，明确告知用户
    throw new Error("该抖音内容是图集（多张图片），不是视频。本服务仅支持视频处理，请选择包含视频的抖音链接。");
  }
  
  // 获取重定向后的mp4视频地址
  // 图集时，视频地址为空，不处理
  let videoMp4Url = "";
  if (videoUrl.length > 0) {
    videoMp4Url = await getVideoRedirectUrl(videoUrl);
  }
  
  const videoInfo = {
    video_url: videoMp4Url,
    cover_url: data.video.cover.url_list[0],
    title: data.desc,
    images: images,
    author: {
      uid: data.author.sec_uid,
      name: data.author.nickname,
      avatar: data.author.avatar_thumb.url_list[0],
    }
  };
  
  console.log("视频解析完成:", {
    title: videoInfo.title,
    author: videoInfo.author.name,
    hasVideo: !!videoInfo.video_url,
    hasImages: videoInfo.images.length > 0
  });
  
  return videoInfo;
}

/**
 * 从分享文本中提取抖音链接
 * @param {string} shareText 分享文本
 * @returns {string|null} 提取到的抖音链接
 */
function extractDouyinUrl(shareText) {
  console.log("开始提取抖音链接:", shareText);
  console.log("输入文本长度:", shareText.length);

  // 支持多种抖音链接格式，优化正则表达式
  const patterns = [
    // v.douyin.com 短链接格式（最常见）
    /https?:\/\/v\.douyin\.com\/[A-Za-z0-9_-]+\/?/g,
    // www.douyin.com 网页版链接
    /https?:\/\/www\.douyin\.com\/video\/\d+/g,
    // iesdouyin.com 内部链接
    /https?:\/\/www\.iesdouyin\.com\/share\/video\/\d+/g,
    // 支持更宽泛的 v.douyin.com 格式
    /https?:\/\/v\.douyin\.com\/[A-Za-z0-9]+/g
  ];

  for (let i = 0; i < patterns.length; i++) {
    const pattern = patterns[i];
    console.log(`尝试模式 ${i + 1}:`, pattern.toString());

    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取抖音链接:", url);
      console.log("匹配的模式:", pattern.toString());
      return url;
    }
  }

  console.log("未找到有效的抖音链接");
  console.log("输入文本预览:", shareText.substring(0, 200));
  return null;
}

/**
 * 从分享文本中提取小红书链接
 * @param {string} shareText 分享文本
 * @returns {string|null} 提取到的小红书链接
 */
function extractRedbookUrl(shareText) {
  console.log("开始提取小红书链接:", shareText);
  
  // 支持多种小红书链接格式
  const patterns = [
    /https?:\/\/www\.xiaohongshu\.com\/explore\/[A-Za-z0-9]+/g,
    /https?:\/\/www\.xiaohongshu\.com\/discovery\/item\/[A-Za-z0-9]+/g,
    /https?:\/\/xhslink\.com\/[A-Za-z0-9\/]+/g,  // 更宽泛的匹配，包含路径
    /http:\/\/xhslink\.com\/[A-Za-z0-9\/]+/g     // 更宽泛的匹配，包含路径
  ];
  
  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取小红书链接:", url);
      return url;
    }
  }
  
  console.log("未找到有效的小红书链接");
  return null;
}

/**
 * 检测平台类型
 * @param {string} shareText 分享文本
 * @returns {string} 平台类型 'douyin' | 'redbook' | 'unknown'
 */
function detectPlatform(shareText) {
  if (shareText.includes("douyin.com") || shareText.includes("抖音")) {
    return "douyin";
  } else if (shareText.includes("xiaohongshu.com") || shareText.includes("xhslink.com") || shareText.includes("小红书")) {
    return "redbook";
  }
  return "unknown";
}

/**
 * 解析小红书分享URL获取内容信息
 * @param {string} shareUrl 分享URL
 * @returns {Promise<Object>} 内容信息
 */
async function parseRedbookShareUrl(shareUrl) {
  console.log("开始解析小红书分享URL:", shareUrl);
  
  // 获取页面内容，设置更高的重定向限制
  const response = await uniCloud.httpclient.request(shareUrl, {
    method: "GET",
    headers: getDefaultHeaders('redbook'),
    followRedirect: true,
    timeout: 15000,
    maxRedirects: 10  // 增加重定向次数限制
  });
  
  if (response.status !== 200) {
    throw new Error(`HTTP请求失败，状态码: ${response.status}`);
  }
  
  const htmlContent = response.data;
  console.log("HTML内容长度:", htmlContent.length);
  
  // 检查是否获取到了小红书的页面
  if (!htmlContent.includes('xiaohongshu') && !htmlContent.includes('小红书')) {
    console.log("页面内容预览:", htmlContent.substring(0, 500));
    throw new Error("未获取到小红书页面内容，可能是链接已失效");
  }
  
  // 使用正则表达式查找 window.__INITIAL_STATE__
  const pattern = /window\.__INITIAL_STATE__\s*=\s*(.*?)<\/script>/s;
  const match = pattern.exec(htmlContent);
  
  if (!match || !match[1]) {
    throw new Error("parse video json info from html fail");
  }
  
  // 解析JSON数据 (需要处理JavaScript语法)
  let jsonData;
  try {
    // 小红书的数据可能包含JavaScript语法，需要处理
    let jsonStr = match[1].trim();
    console.log("原始数据长度:", jsonStr.length);
    console.log("原始数据开头:", jsonStr.substring(0, 200));
    
    // 处理undefined值
    jsonStr = jsonStr.replace(/:\s*undefined/g, ': null');
    
    // 处理函数调用和其他JavaScript语法
    jsonStr = jsonStr.replace(/:\s*function\s*\([^)]*\)\s*\{[^}]*\}/g, ': null');
    
    // 处理正则表达式
    jsonStr = jsonStr.replace(/:\s*\/[^/]+\/[gimuy]*/g, ': null');
    
    // 处理单引号字符串（转换为双引号）
    jsonStr = jsonStr.replace(/:\s*'([^']*)'/g, ': "$1"');
    
    // 处理数字后面的注释
    jsonStr = jsonStr.replace(/(\d+)\s*\/\/[^\n\r]*/g, '$1');
    
    // 处理对象key没有引号的情况
    jsonStr = jsonStr.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
    
    // 移除JavaScript注释
    jsonStr = jsonStr.replace(/\/\/.*$/gm, '');
    jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // 处理尾随逗号
    jsonStr = jsonStr.replace(/,\s*([}\]])/g, '$1');
    
    console.log("处理后数据开头:", jsonStr.substring(0, 200));
    
    jsonData = JSON.parse(jsonStr);
    console.log("JSON解析成功");
  } catch (error) {
    console.error("JSON解析失败:", error.message);
    console.log("解析失败的位置信息:", error.toString());
    
    // 尝试更简单的方法：寻找并提取对象的主要部分
    try {
      let simpleStr = match[1].trim();
      
      // 如果是以{开头，找到对应的}结尾
      let braceCount = 0;
      let validJsonEnd = -1;
      
      for (let i = 0; i < simpleStr.length; i++) {
        if (simpleStr[i] === '{') {
          braceCount++;
        } else if (simpleStr[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            validJsonEnd = i;
            break;
          }
        }
      }
      
      if (validJsonEnd > 0) {
        simpleStr = simpleStr.substring(0, validJsonEnd + 1);
        console.log("尝试截取有效JSON部分，长度:", simpleStr.length);
        
        // 再次处理
        simpleStr = simpleStr.replace(/:\s*undefined/g, ': null');
        simpleStr = simpleStr.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
        simpleStr = simpleStr.replace(/,\s*([}\]])/g, '$1');
        
        jsonData = JSON.parse(simpleStr);
        console.log("简化JSON解析成功");
      } else {
        throw new Error("无法找到完整的JSON结构");
      }
    } catch (simpleError) {
      console.error("简化JSON解析也失败:", simpleError.message);
      throw new Error("JSON数据解析失败，页面结构可能已变更");
    }
  }
  
  const noteId = jsonData.note.currentNoteId;
  // 验证返回：小红书的分享链接有有效期，过期后会返回 undefined
  if (noteId === "undefined" || !noteId) {
    throw new Error("parse fail: note id in response is undefined");
  }
  
  const data = jsonData.note.noteDetailMap[noteId].note;
  
  // 视频地址
  let videoUrl = "";
  const h264Data = data?.video?.media?.stream?.h264 || [];
  if (h264Data.length > 0) {
    videoUrl = h264Data[0].masterUrl || "";
  }
  
  // 获取图集图片地址
  const images = [];
  if (!videoUrl && data.imageList) {
    for (const imgItem of data.imageList) {
      // 个别图片有水印, 替换图片域名
      const imageId = imgItem.urlDefault.split("/").pop().split("!")[0];
      // 如果链接中带有 spectrum/ , 替换域名时需要带上
      const spectrumStr = imgItem.urlDefault.includes("spectrum") ? "spectrum/" : "";
      const newUrl = `https://ci.xiaohongshu.com/notes_pre_post/${spectrumStr}${imageId}?imageView2/format/jpg`;
      
      const imgInfo = {
        url: newUrl,
        live_photo_url: ""
      };
      
      // 是否有 livephoto 视频地址
      if (imgItem.livePhoto && imgItem.stream?.h264?.length > 0) {
        imgInfo.live_photo_url = imgItem.stream.h264[0].masterUrl;
      }
      
      images.push(imgInfo);
    }
  }
  
  const contentInfo = {
    video_url: videoUrl,
    cover_url: data.imageList[0].urlDefault,
    title: data.title,
    images: images,
    author: {
      uid: data.user.userId,
      name: data.user.nickname,
      avatar: data.user.avatar,
    }
  };
  
  console.log("小红书内容解析完成:", {
    title: contentInfo.title,
    author: contentInfo.author.name,
    hasVideo: !!contentInfo.video_url,
    hasImages: contentInfo.images.length > 0
  });
  
  return contentInfo;
}

// 云函数主入口
exports.main = async (event) => {
  console.log("=== 视频解析云函数开始执行 ===");
  console.log("输入参数:", {
    url: event.url ? event.url.substring(0, 100) + (event.url.length > 100 ? '...' : '') : 'undefined',
    openid: event.openid ? '***' : 'undefined',
    userId: event.userId ? '***' : 'undefined',
    timestamp: new Date().toISOString()
  });

  const { url: inputText, openid, userId } = event;
  
  // 参数验证
  if (!inputText) {
    return {
      code: -1,
      message: "缺少必要参数：url（可以是链接或分享文本）",
    };
  }
  
  if (typeof inputText !== "string" || inputText.trim().length === 0) {
    return {
      code: -1,
      message: "参数url必须是非空字符串",
    };
  }
  
  if (!openid && !userId) {
    return {
      code: -1,
      message: "缺少用户标识参数：openid 或 userId",
    };
  }
  
  // 检测平台类型
  console.log("开始检测平台类型...");
  const platform = detectPlatform(inputText);
  console.log("检测到的平台:", platform);

  if (platform === "unknown") {
    console.log("平台检测失败，输入内容:", inputText.substring(0, 200));
    return {
      code: -1,
      message: "不支持的平台，目前支持抖音和小红书",
    };
  }
  
  try {
    // 用户权限验证（如果提供了用户信息）
    if (openid || userId) {
      const userCollection = db.collection("users");
      const userQuery = openid ? { openid } : { _id: userId };
      const userResult = await userCollection.where(userQuery).get();
      
      if (userResult.data.length === 0) {
        return {
          code: -1,
          message: "用户不存在，请先登录",
        };
      }
    }
    
    let result;
    
    if (platform === "douyin") {
      // 解析抖音
      console.log("开始解析抖音内容...");
      const douyinUrl = extractDouyinUrl(inputText);
      if (!douyinUrl) {
        console.error("抖音链接提取失败");
        throw new Error("未找到有效的抖音链接");
      }
      console.log("提取到的抖音链接:", douyinUrl);
      result = await parseShareUrl(douyinUrl);
    } else if (platform === "redbook") {
      // 解析小红书
      console.log("开始解析小红书内容...");
      const redbookUrl = extractRedbookUrl(inputText);
      if (!redbookUrl) {
        console.error("小红书链接提取失败");
        throw new Error("未找到有效的小红书链接");
      }
      console.log("提取到的小红书链接:", redbookUrl);
      result = await parseRedbookShareUrl(redbookUrl);
    }
    
    // 检查解析结果是否为视频
    const isVideo = result.video_url && result.video_url.length > 0;
    const hasImages = result.images && result.images.length > 0;
    
    if (!isVideo) {
      return {
        code: -1,
        message: hasImages 
          ? `该${platform === 'douyin' ? '抖音' : '小红书'}内容是图集，不是视频。本服务仅支持视频处理，请选择包含视频的链接。` 
          : `该${platform === 'douyin' ? '抖音' : '小红书'}内容没有有效的视频，请检查链接是否正确。`,
        data: {
          platform: platform,
          type: "image_gallery",
          title: result.title,
          author: {
            name: result.author.name,
            uid: result.author.uid,
            avatar: result.author.avatar,
          },
          images: result.images || [],
          reason: "not_video"
        },
      };
    }
    
    // 如果是视频，返回视频信息（不在云函数中下载视频）
    console.log("视频解析成功，返回视频信息...");
    
    return {
      code: 0,
      message: "视频解析成功",
      data: {
        platform: platform,
        type: "video",
        title: result.title,
        author: {
          name: result.author.name,
          uid: result.author.uid,
          avatar: result.author.avatar,
        },
        video: {
          url: result.video_url,
          cover: result.cover_url,
        }
      },
    };
    
  } catch (error) {
    console.error("=== 视频解析失败 ===");
    console.error("错误类型:", error.constructor.name);
    console.error("错误消息:", error.message);
    console.error("错误堆栈:", error.stack);
    console.error("输入链接:", inputText);
    console.error("检测平台:", platform);

    // 根据错误类型返回不同的错误信息
    let errorMessage = "视频解析失败";

    if (error.message.includes("该抖音内容是图集")) {
      errorMessage = "该抖音内容是图集（多张图片），不是视频。本服务仅支持视频处理，请选择包含视频的抖音链接。";
    } else if (error.message.includes("该链接指向的是图集内容")) {
      errorMessage = "该链接指向的是图集内容，本服务仅支持视频处理，请选择包含视频的抖音链接。";
    } else if (error.message.includes("未找到有效的抖音链接")) {
      errorMessage = "未找到有效的抖音链接，请检查输入内容是否包含正确的抖音分享链接";
    } else if (error.message.includes("未找到有效的小红书链接")) {
      errorMessage = "未找到有效的小红书链接，请检查输入内容";
    } else if (error.message.includes("链接重定向失败")) {
      errorMessage = "抖音链接重定向失败，可能是链接已失效，请尝试使用最新的分享链接";
    } else if (error.message.includes("无法获取重定向URL")) {
      errorMessage = "链接解析失败，请尝试使用最新的分享链接";
    } else if (error.message.includes("页面数据结构已变更")) {
      errorMessage = "抖音页面结构已变更，请联系开发者更新解析逻辑";
    } else if (error.message.includes("无法识别内容类型")) {
      errorMessage = "无法识别内容类型，可能是抖音页面结构发生变化";
    } else if (error.message.includes("parse video json info from html fail")) {
      errorMessage = "页面数据格式已变更，请联系开发者更新解析逻辑";
    } else if (error.message.includes("内容不存在或已被删除")) {
      errorMessage = "视频内容不存在或已被删除，请检查链接是否有效";
    } else if (error.message.includes("failed to parse video info from HTML")) {
      errorMessage = "视频不存在或已被删除";
    } else if (error.message.includes("parse fail: note id in response is undefined")) {
      errorMessage = "小红书链接已过期或内容不存在，请使用最新的分享链接";
    } else if (error.message.includes("未获取到小红书页面内容")) {
      errorMessage = "无法访问小红书页面，可能是链接已失效或网络问题";
    } else if (error.message.includes("HTTP请求失败")) {
      errorMessage = "网络请求失败，请稍后重试";
    } else if (error.message.includes("JSON数据解析失败")) {
      errorMessage = "数据解析失败，可能是页面结构发生变化";
    } else if (error.message.includes("failed to parse Videos or Photo Gallery info from json")) {
      errorMessage = "无法解析视频或图集信息，请检查链接是否有效";
    }
    
    return {
      code: -1,
      message: errorMessage,
      error: error.message,
    };
  }
};